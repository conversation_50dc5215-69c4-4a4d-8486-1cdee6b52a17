TranslationsEN = {
        racing_map = 'Map',
        set_waypoint = 'Set Waypoint',
        view_records = 'View Records',
        fee = "Selling fee",
        you_get = "You get",
        recipient_racer_name = 'Recipient Racer Name',
        transfer = 'Transfer',
        sell = "Sell",
        crypto_hint = 'Only whole numbers. Both amounts need to be 1 or above',
        view_records_modal_desc = 'Here you can view all records on a track and remove them.',
        changes_are_permanent = 'Removing records is permanent and can not be reverted',
        remove_record = 'Remove record',
        confirm_record_removal = 'Confirm record removal',
        transfer_succ = 'Transfered Crypto to ',
        transfer_succ_rec = 'Recieved Crypto from ',
        host_silent = 'Host without notification',
        purchase = 'Purchase',
        crypto_amount = 'Crypto amount',
        USER_DOES_NOT_EXIST = 'Error: User does not exist',
        NOT_ENOUGH = 'Error: Not enough to pay',
        total_cost = 'Total cost',
        purchased_crypto = 'Successfully purchased ',
        sold_crypto = 'Successfully sold',
        accept = "Accept",
        access_for = "access for",
        access_list = "Access List",
        access_race = "Race access by citizenId. Separate by commas",
        access_updated = "Access updated",
        active = "Active",
        head2head = "H2H",
        add_checkpoint = "Add Checkpoint",
        all = 'All Classes',
        already_in_race = "You are already in a race.",
        already_making_race = "You are already making a race.",
        any = "Any Type",
        are_you_sure_you_want_to_clear = 'Clear this tracks leaderboard permanently?',
        are_you_sure_you_want_to_delete_track = 'Delete this track permanently?',
        auth = "Auth",
        change_auth = "Change racer authority",
        auth_no = "No auth",
        available = "Available",
        available_races = "Available Races",
        available_races_txt = "See all the currently available races right now.",
        bad_input = "'Input is invalid",
        base_wps = "Use basic waypoints",
        basic_wps_off = "Your Racing GPS will not show basic waypoints",
        basic_wps_on = "Your Racing GPS will show basic waypoints",
        best = "best",
        best_lap = "Best Lap",
        bounties = 'Bounties',
        bounties_desc = 'Bounties will be listed here for you to complete. You can claim the bounty by beating the lap time in any race as long as you fullfill the requirements.',
        bounties_desc_2 = 'After a bounty has been claimed you can not claim it again, but if you beat your own previous time you will get a (possibly reduced) payout.',
        bounties_have_been_generated = ' bounties were created.',
        bounty_claimed = 'You claimed a bounty worth $',
        buy_in = "Buy in",
        can_be_reverted = "Can be reverted",
        can_not_afford = "You can't afford ",
        cancel_race = 'Cancel Race',
        cancel_race_forced = 'Force Cancel Race',
        start_race_forced = 'Force start race',
        cant_be_reverted = "Can't be reverted",
        cant_decode = "Not possible to decode input",
        checkpoint_2nd = "2nd",
        checkpoint_3rd = "3rd",
        checkpoint_left = "Left Checkpoint",
        checkpoint_next = "Next",
        checkpoint_right = "Right Checkpoint",
        checkpoints = "Checkpoints",
        choose_a_class = "Choose a Class",
        choose_a_track = "Choose a Track",
        circuit = 'Circuit',
        circuit_only = "Circuit only",
        citizen_id = "Citizen Id",
        class = "Class",
        class_car = " class car", -- note the spaces on this,
        clear_lead = "Clear Leaderboard",
        clear_lead_for = "Clear Leaderboard for",
        clear_leaderboard = "Clear Leaderboard",
        close = "Close",
        close_editor = "Close editor",
        closest_checkpoint = "Closest Checkpoint",
        confirm = "Confirm",
        in_h2h = "In H2H",
        leave_h2h = "Leave current H2H race",
        copy_checkpoints = "Copy Checkpoints",
        corrupt_data = "Checkpoint data is corrupt",
        could_not_find_person = "Could not find the person",
        create_crew = "Create a new crew",
        create_racer = "Create Racer",
        create_racing_fob_command = 'createracingfob',
        create_racing_fob_description = 'Create a Racing GPS (Admin)',
        create_track = "Create Track",
        create_user = "Create a user",
        create_with_editor = "Create With Race Editor",
        create_with_share = "Create With track Share",
        creator = "Creator",
        crew = "Crew",
        crew_invite_accepted = "A racer has accepted to join your crew",
        crew_invite_rejected = "A racer has denied to join your crew",
        crew_rankings = "Crew rankings",
        crew_stats = "Crew Stats",
        curated = 'Curated',
        curation = "Curation Settings",
        currency = "Participation currency",
        currency_text = "$",
        current_conversionrate = "Current conversion rate",
        current = "Current",
        current_race = "Current Race",
        current_race_txt = "Options for your currently entered race.",
        currently_in = "You're currently in a ", -- note the spaces on this
        delete_checkpoint = "Delete Checkpoint",
        delete_track = "Delete track",
        delete_user = "Delete User",
        deny = "Deny",
        head2head_invite = 'Head 2 Head invitation from',
        description = 'Description',
        description_hint = 'A short description of the track',
        disband_crew = "Disband current crew",
        disband_crew_first = "Disband your current crew first",
        disbanded_crew = "Your racing crew has been disbanded",
        display_tracks = "Displaying track on your map for 20 seconds",
        distance_check = "Distance check for positions",
        distance_info = "Having this on might have an impact on your performance if there are many racers in a race",
        distance_off = "Position checks won't use distance",
        distance_on = "Position checks will use distance",
        draw_text_wps_off = "Your Racing GPS will not show pillar waypoints",
        draw_text_wps_on = "Your Racing GPS will show pillar waypoints",
        edit_access = "Edit access",
        edit_checkpoint_header = "Edit checkpoint",
        edit_settings = "Edit Settings",
        edit_track = "Edit track",
        editing_access_for = "Editing access for",
        editing_access_info = "*Racer Names, separated by commas",
        editor_canceled = "You canceled the editor.",
        editor_confirm = "Press [9] again to confirm.",
        eliminated = "You were eliminated",
        elimination = "Elimination",
        error_lacking_user = "You are lacking a racing app user",
        error_lacking_user_desc = "You need to have a user selected to access this. If you have one you can select it in settings",
        error_no_user = "No User selected",
        error_no_user_desc = "Select a user in the settings page",
        error_removed = "Your current user has been permanently removed",
        error_removed_desc = "Sucks to suck I guess",
        error_revoked = "Your current user has had it's access revoked",
        error_revoked_desc = "It has not been removed, yet",
        esc = "Press ESC to close",
        expires = "Expires",
        extra_payout = "Extra Payout",
        finish = "Finish",
        finished = "Finished",
        first_person = "First Person",
        founder = "Founder",
        founder_can_not_leave = "The Founder can not leave the crew",
        get_in_vehicle = "Get in a vehicle to start!",
        get_ready = "Get Ready!",
        ghosting = "Ghosting",
        ghostingTime = "Time (in seconds) until Ghosting turns off",
        go = "GO!",
        go_back = "Go Back",
        gps_straight_off = "Your Racing GPS will follow roads",
        gps_straight_on = "Your Racing GPS will go straight between checkpoints",
        handle_curation_for = "Handle curation for",
        has_been_removed = " has been removed",
        hosted_by = "Hosted by",
        hosting = "Hosting",
        id_not_found = "Citizen by that ID was not found.",
        ignore_roads = "GPS ignores roads",
        incorrect_class = "Your vehicle class is incorrect for this race",
        invalid_fob_type = "Invalid GPS type.",
        invite = "Invite",
        invite_sent = "Invite sent",
        you_got_an_invite_h2h = "You recieved an invite to a Head 2 Head race",
        head2head_title = 'Head 2 Head',
        head2head_subtitle = 'A Head 2 Head race is a race where you challange the person closes to you to a quick race. After the challenge has been accepted the racers will be given a random location to race to. First there wins.',
        invite_closest = "Invite closest person",
        invite_from_crew = "Invite from crew",
        invites = "Invites",
        is_first_user = "You're the first user in the database. Your account will be created as a GOD account",
        join_race = "Join Race",
        kicked_cheese = "You got kicked out of the race for attempting to cheat",
        kicked_idling = "You got kicked out of the race for idling",
        kicked_line = "You got disqualified for trying to start pass the line",
        lap = "LAP",
        laps = "Laps",
        leaderboard_has_been_cleared = "leaderboard has been cleared",
        leave = "Leave",
        leave_current_crew = "Leave current crew",
        leave_race = "Leave Race",
        length = "Length",
        manage = "Manage",
        max_checkpoints = "Too many checkpoints might cause issues. Max suggested limit is ",
        max_class = "Max class",
        max_tire_distance = "The max distance allowed is ",
        max_tracks = "You already have the max amount of tracks: ",
        max_uniques = "Max unique names per person:",
        min_tire_distance = "The min distance allowed is ",
        modify_checkpoint = "Modify Checkpoint Menu",
        my_crew = "My crew",
        my_racers = "My Racers",
        my_tracks = "My tracks",
        name_is_used = "Name is used: ",
        name_taken = "Name is taken",
        name_too_long = 'The name is too long.',
        name_too_short = 'The name is too short.',
        name_track = "Name your track",
        name_track_question = "What do you want your track to be named?",
        need_a_name = "The track need to have a name",
        new_host = 'You are the new organizer',
        new_pb = "You got a new personal best!",
        new_rank = "New rank:",
        next_lap = "Next Lap",
        no = "No",
        race_admin = "Admin menu",
        no_available_tracks = "There are no available tracks at the moment to use.",
        no_bounties = 'No bounties available',
        no_checkpoints_to_delete = "You have not placed any checkpoints to delete.",
        no_checkpoints_to_edit = "No checkpoints to edit",
        no_class_limit = "No class limit",
        no_data = "No data yet",
        no_invites = "No invites pending",
        no_members_in_crew = "No members in this crew yet",
        no_name_track = "This track need to have a name",
        no_pending_races = "There are no pending races at the moment.",
        no_permission = "You do not have permission to do that.",
        no_races = "No race active",
        no_races_exist = "No times have been set on this track",
        no_results = "No Results to browse yet",
        no_tracks_exist = "No Tracks Available",
        no_track_found = "No track found with id:",
        not_auth = "Not Authorized",
        enable_hosting = "Allow hosting races",
        hosting_not_allowed = "Race hosting is currently not allowed",
        enable_auto_hosting = "Allow automated races",
        not_close_enough_to_join = "Not close enough to join. A waypoint was set to the start.",
        not_done_yet = "No racers have passed the finish line yet",
        not_enough_checkpoints = "You need a minimum number of checkpoints to save",
        not_enough_money = "Not enough money to join",
        not_in_a_vehicle = "You are not the driver of a vehicle",
        not_in_crew = "You are not in a crew",
        not_in_race = "You are not in a race.",
        number_laps = "Number of Laps",
        off = "Off",
        on = "On",
        open_track_editor_for = "Open track editor for",
        open_tuning_overlay = "Toggle Tuning Overlay",
        participation_amount = "Participation Amount",
        participation_info = "This amount will be handed out to all racers who participate",
        participation_trophy = "You got $",
        participation_trophy_crypto = "You got ",
        pending_crew_invite = "You have a pending invite for the racing crew",
        person_no_exist = "This person does not exist",
        pick_track = "Pick a track",
        pillar_columns = "Show floating pillar Waypoints",
        pos = "POS",
        pot = "Pot",
        price = 'Price',
        prox_error = "No one close enough",
        quick_host = 'Quick host',
        race_already_started = "The race has already started!",
        race_canceled = "The race was canceled",
        race_created = "The race was created!",
        race_doesnt_exist = "This race does not exist",
        race_go = "GO!",
        race_info = "%s lap(s) | %sm | %s racer(s)",
        race_joined = "You joined the race.",
        race_last_person = "You were the last person in that race so it was canceled.",
        race_name_exists = "There is already a race with that name.",
        race_no_exist = "Race doesn't exist anymore",
        race_record = "You now hold the record in %s with a time of: %s!",
        race_records = "Race Records",
        race_records_txt = "See all records for races.",
        race_results = "Recent Races",
        race_results_txt = "See results from previous races",
        race_saved = "The race was saved",
        race_someone_joined = "Someone has joined the race.",
        race_someone_left = "Someone has left the race.",
        race_timed_out = "The race timed out and was canceled.",
        race_type = "Race type",
        race_will_start = "The race will start in 10 seconds.",
        racer_already_in_crew = "This racer is already in the crew",
        racer_finished_place = "finished in place: ",
        racer_id = "Paypal/TempId (leave empty for self)",
        racer_name = "Racer Name",
        racer_rankings = "Racer rankings",
        racer_records = "Racer Records",
        racer_s = "Racer(s)",
        racers = "Racers",
        races = "Races",
        racing = "Racing",
        racing_setup = "Racing - Setup",
        rank = "Rank",
        rank_change = "Rank Change",
        rank_update = "Your ranking has been updated with",
        ranked = "Ranked",
        ready_to_race = "Ready to Race, ",
        refresh = "Refresh",
        remove_crypto = "Exchanged ",
        removed_user = "Your selected racing user been deleted",
        required_rank = 'Rank Req',
        reroll_bounties = 'Re-roll bounties',
        trigger_new_autohost = "Create new Auto Host",
        results = "Results",
        return_to_start = "Return to the start or you will be kicked from the race: ",
        reversed = "Reversed",
        revoke = "Revoke",
        revoked_access = "Your current racing user has had it's access changed",
        save_track = "Save Track",
        search_dot = "Search...",
        see_records = 'Records',
        select_race = "Select Race",
        select_track = "Select Track",
        select_track_to_view = "Select a track to view results",
        selected_track = "Selected Track",
        set_curated = "Curate",
        set_uncurated = "Un-Curate",
        settings = "Settings",
        setup = "Setup",
        setup_race = "Setup Race",
        shared_with = "Shared with",
        show_gps = "Show GPS Route",
        show_track = "Show Track",
        showing_all = "Showing all tracks",
        showing_curated = "Showing curated tracks",
        slow_down = "You can't go that fast!",
        sprint = "Sprint",
        sprint_only = "Sprint only",
        start_race = "Start Race",
        starting_line = "Start Line",
        starts = "Starts",
        starts_in = "Starts in",
        time = "Time",
        time_added = "Your time has been added to the leaderboard",
        tire_distance = "Tire distance",
        to_many_names = "This person has enough unique Racer Names already...",
        toggled_gps_route_off = "You have toggled GPS Route OFF",
        toggled_gps_route_on = "You have toggled GPS Route ON",
        total = "total",
        track = "track",
        track_id = "Track ID",
        track_name = "Track Name",
        track_records = 'Track Records',
        tracks = "Tracks",
        type = "Type",
        unclaimed = "Unclaimed Record!",
        unknown = "Unknown",
        unowned_dongle = "It doesn't seem to respond do you.",
        useGhosting = "Use Ghosting?",
        user = "User",
        user_list_updated = "Racing user list updated",
        user_no = "No user",
        vehicle = "Vehicle",
        winner = "Winner",
        wins = "Wins",
        you_have_to_place_a_new_checkpoint_down_first = "You have to place down a new checkpoint first",
        invalid_vehicle_plate = "Invalid vehicle plate detected",
        vehicle_changed = "Vehicle changed during race - race invalidated",
}
