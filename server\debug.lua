DebugRaceResults = {
    ["RI-DEBUG"] = {
        Data = {
            FinishTime = os.time(),
            Ghosting = false,
            SetupRacerName = "PAODPOAS2",
            BuyIn = 0,
            Laps = 3,
            MaxClass = "",
            GhostingTime = 0,
            RaceId = "RI-DEBUG",
            TrackId = "LR-7666",
            RaceData = {
                Ghosting = false,
                Started = false,
                Waiting = false,
                Records = {
                    {
                        Holder = "mamamamamam",
                        Time = 24262,
                        Class = "X",
                        Vehicle = "Osiris FR",
                        RaceType = "Circuit",
                    },
                    {
                        Holder = "mamamamamam",
                        Time = 26305,
                        Class = "S",
                        Vehicle = "model not found",
                        RaceType = "Sprint",
                    },
                },
                Distance = 1045,
                Creator = "SYY99260",
                BuyIn = 0,
                Racers = {},
                GhostingTime = 0,
                SetupCitizenId = "SYY99260",
                CreatorName = "xXxCoolChadxXx69",
                RaceId = "LR-7666",
                Access = {},
                RaceName = "Elysian But fake",
            },
            SetupCitizenId = "SYY99260"
        },
        Result = {
            {
                VehicleModel = "Euros ZR300",
                RacerName = "PAODPOAS2",
                TotalTime = 74128,
                CarClass = "S",
                BestLap = 30404
            }
        }
    },
    ["RI-TEST"] = {
        Data = {
            FinishTime = os.time() - 500,
            Ghosting = false,
            SetupRacerName = "PAODPOAS2",
            BuyIn = 0,
            Laps = 0,
            MaxClass = "",
            GhostingTime = 0,
            RaceId = "RI-TEST",
            TrackId = "LR-1123",
            RaceData = {
                Ghosting = false,
                Started = false,
                Waiting = false,
                Ranked = true,
                Records = {
                    {
                        Holder = "mamamamamam",
                        Time = 24262,
                        Class = "X",
                        Vehicle = "Osiris FR",
                        RaceType = "Circuit",

                    },
                    {
                        Holder = "mamamamamam",
                        Time = 26305,
                        Class = "S",
                        Vehicle = "model not found",
                        RaceType = "Sprint",
                    },
                },
                Distance = 1045,
                Creator = "SYY99260",
                BuyIn = 0,
                Racers = {},
                GhostingTime = 0,
                SetupCitizenId = "SYY99260",
                CreatorName = "xXxCoolChadxXx69",
                RaceId = "LR-7666",
                Access = {},
                RaceName = "Not Elysian",
            },
            SetupCitizenId = "SYY99260"
        },
        Result = {
            {
                VehicleModel = "A cool car",
                RacerName = "YOMOM",
                TotalTime = 134128,
                CarClass = "A",
                BestLap = 1231,
                Ranking = 15,
                TotalChange = -11
            },
            {
                VehicleModel = "Euros ZR300",
                RacerName = "PAODPOAS2",
                RacingCrew = "Cool Peoples Crew",
                TotalTime = 34128,
                CarClass = "S",
                BestLap = 12353,
                Ranking = 10,
                TotalChange = 1
            },
        }
    },
    ['RI-MADEUP'] = {
        Data = {
            FinishTime = os.time() - 900,
            Ghosting = false,
            SetupRacerName = "DEBUG NAME",
            BuyIn = 0,
            Laps = 0,
            MaxClass = "",
            GhostingTime = 0,
            RaceId = "RI-MADEUP",
            TrackId = "LR-1123",
            RaceData = {
                Ghosting = false,
                Started = false,
                Waiting = false,
                Ranked = true,
                Records = {
                    {
                        Holder = "mamamamamam",
                        Time = 24262,
                        Class = "X",
                        Vehicle = "Osiris FR",
                        RaceType = "Circuit",

                    },
                    {
                        Holder = "mamamamamam",
                        Time = 26305,
                        Class = "S",
                        Vehicle = "model not found",
                        RaceType = "Sprint",
                    },
                },
                Distance = 1045,
                Creator = "SYY99260",
                BuyIn = 0,
                Racers = {},
                GhostingTime = 0,
                SetupCitizenId = "SYY99260",
                CreatorName = "xXxCoolChadxXx69",
                RaceId = "LR-7666",
                Access = {},
                RaceName = "Not Elysian",
            },
            SetupCitizenId = "SYY99260"
        },
        Result = {
            {
                VehicleModel = "A cool car",
                RacerName = "YOMOM",
                TotalTime = 284128,
                CarClass = "A",
                BestLap = 1231999,
                Ranking = 15,
                TotalChange = -11
            },
            {
                VehicleModel = "Euros ZR300",
                RacerName = "DEBUG NAME",
                RacingCrew = "Cool Peoples Crew",
                TotalTime = 31128,
                CarClass = "S",
                BestLap = 10353,
                Ranking = 10,
                TotalChange = 1
            },
        }
    }
}
