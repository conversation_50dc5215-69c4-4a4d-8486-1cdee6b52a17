{"name": "racing-app", "version": "4.2.0", "type": "module", "private": true, "engines": {"node": "^22.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "type-check": "vue-tsc --noEmit", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore", "watch": "vite build --watch "}, "dependencies": {"@mdi/font": "7.4.47", "axios": "^1.8.4", "core-js": "^3.36.1", "maplibre-gl": "^3.6.2", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-maplibre-gl": "^5.5.9", "vue-router": "^4.5.0", "vuetify": "^3.8.1", "webfontloader": "^1.6.28"}, "devDependencies": {"@babel/types": "^7.24.0", "@types/node": "^20.11.30", "@types/webfontloader": "^1.6.38", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-typescript": "^14.5.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "sass": "^1.86.3", "typescript": "^5.4.2", "vite": "^5.3.3", "vite-plugin-vuetify": "^2.0.1", "vue-tsc": "^2.0.6"}}