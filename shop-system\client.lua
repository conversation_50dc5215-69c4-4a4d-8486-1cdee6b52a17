local Framework = exports['no-core']:GetCoreObject()
Framework.Shared.Items = exports.ox_inventory:Items()

-- Local variables
local shopPed = nil
local shopBlip = nil
local spawn = false

-- Create shop blip
local function CreateBlip()
    if not Config.ShopLocation.blip then return end
    
    shopBlip = AddBlipForCoord(Config.ShopLocation.coords.xyz)
    SetBlipSprite(shopBlip, Config.ShopLocation.blip.sprite)
    SetBlipColour(shopBlip, Config.ShopLocation.blip.color)
    SetBlipScale(shopBlip, Config.ShopLocation.blip.scale)
    SetBlipAsShortRange(shopBlip, true)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(Config.ShopLocation.blip.name)
    EndTextCommandSetBlipName(shopBlip)
end

-- Create shop ped
local function CreateShopPed()
    if spawn then return end
    
    local model = Config.ShopLocation.ped
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    
    shopPed = CreatePed(4, model, Config.ShopLocation.coords.x, Config.ShopLocation.coords.y, Config.ShopLocation.coords.z - 1.0, Config.ShopLocation.coords.w, false, false)
    SetEntityHeading(shopPed, Config.ShopLocation.coords.w)
    FreezeEntityPosition(shopPed, true)
    SetEntityInvincible(shopPed, true)
    SetBlockingOfNonTemporaryEvents(shopPed, true)
    
    -- Add target interaction
    exports['nocore-eye']:AddCircleZone(Config.ShopLocation.target.name, 
        vector3(Config.ShopLocation.coords.x, Config.ShopLocation.coords.y, Config.ShopLocation.coords.z), 
        1.0,
        {
            name = Config.Shop.label,
            debugPoly = Config.Debug,
            useZ = true,
        },
        {
            options = {
                {
                    event = "shop-system:client:OpenShopMenu",
                    icon = Config.ShopLocation.target.icon,
                    label = Config.ShopLocation.target.label,
                }
            },
            distance = 2.0
        }
    )
    
    spawn = true
end

-- Event handlers
RegisterNetEvent('Framework:Client:OnPlayerLoaded', function()
    CreateBlip()
    CreateShopPed()
end)

-- Main shop menu
RegisterNetEvent('shop-system:client:OpenShopMenu', function()
    local mainMenu = {
        {
            title = Config.UI.mainMenu.buyTitle,
            description = Config.UI.mainMenu.buyDescription,
            icon = "cart-shopping",
            event = "shop-system:client:OpenBuyMenu"
        },
        {
            title = Config.UI.mainMenu.sellTitle,
            description = Config.UI.mainMenu.sellDescription,
            icon = "dollar-sign",
            event = "shop-system:client:OpenSellMenu"
        }
    }
    
    lib.registerContext({
        id = 'tuning_shop_main',
        title = Config.Shop.label,
        options = mainMenu
    })
    
    lib.showContext('tuning_shop_main')
end)

-- Buy menu
RegisterNetEvent('shop-system:client:OpenBuyMenu', function()
    exports.ox_inventory:openInventory('shop', { type = Config.Shop.type })
end)

-- Sell menu
RegisterNetEvent('shop-system:client:OpenSellMenu', function()
    local sellMenu = {}
    
    for _, item in ipairs(Config.Items) do
        local itemData = Framework.Shared.Items[item.name]
        if itemData then
            local itemCount = exports.ox_inventory:GetItemCount(item.name)
            local disabled = itemCount == 0
            
            sellMenu[#sellMenu + 1] = {
                title = itemData.label,
                description = ("%s\nYou have: %d"):format(Config.UI.sellMenu.sellFormat:format(item.basePrice), itemCount),
                icon = ("nui://ox_inventory/web/images/%s.png"):format(item.name),
                disabled = disabled,
                event = 'shop-system:client:SellItems',
                args = { item = item.name, count = itemCount }
            }
        end
    end
    
    lib.registerContext({
        id = 'tuning_shop_sell',
        title = Config.UI.sellMenu.title,
        menu = 'tuning_shop_main',
        options = sellMenu
    })
    
    lib.showContext('tuning_shop_sell')
end)

RegisterNetEvent('shop-system:client:SellItems', function(data)
    if data.count <= 0 then return end
    
    local success = lib.callback.await('shop-system:Selling', false, data)
    if success then
        Framework.Functions.Notify(Config.UI.sellMenu.successMessage, 'success')
    end
end)

-- Resource cleanup
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        if shopPed then
            DeleteEntity(shopPed)
        end
        if shopBlip then
            RemoveBlip(shopBlip)
        end
    end
end) 