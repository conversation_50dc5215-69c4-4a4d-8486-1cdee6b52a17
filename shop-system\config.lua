Config = Config or {}

-- Shop Configuration
Config.ShopMultiplier = 2 -- Price multiplier for buying items (buying price = base price * multiplier)
Config.Debug = false

-- Shop Identifiers
Config.Shop = {
    id = 'tuning', -- Unique identifier for the shop (used in database and ox_inventory)
    type = 'tuning', -- Shop type for ox_inventory
    label = 'Tuning Shop', -- Display name of the shop
    stash = {
        id = 'tuning', -- Stash identifier
        label = 'Tuning Shop Storage', -- Stash label
        weight = 1, -- Stash weight limit multiplier
    }
}

-- Shop Items Configuration base price is the price of the item in the shop the player can sell to the shop maxCount is the max amount of items the player can sell to the shop count is the amount of items the player has in their inventory  
Config.Items = {
    { slot = 1, name = "nitrous_install_kit", basePrice = 7500, count = 0, maxCount = 100 },
    { slot = 2, name = "nitrous_bottle", basePrice = 2500, count = 0, maxCount = 100 },
    { slot = 3, name = "fakeplate", basePrice = 500, count = 0, maxCount = 100 },
    { slot = 4, name = "lighting_controller", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 5, name = "duct_tape", basePrice = 100, count = 0, maxCount = 100 },
    { slot = 6, name = "engine_oil", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 7, name = "tyre_replacement", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 8, name = "clutch_replacement", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 9, name = "air_filter", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 10, name = "spark_plug", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 11, name = "brakepad_replacement", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 12, name = "suspension_parts", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 13, name = "awd_drivetrain", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 14, name = "rwd_drivetrain", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 15, name = "fwd_drivetrain", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 16, name = "slick_tyres", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 17, name = "semi_slick_tyres", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 18, name = "offroad_tyres", basePrice = 1000, count = 0, maxCount = 100 },
    { slot = 19, name = "drift_tuning_kit", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 20, name = "r488sound", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 21, name = "k20a", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 22, name = "urusv8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 23, name = "m297zonda", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 24, name = "v8engine", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 25, name = "shonen", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 26, name = "predatorv8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 27, name = "gt3flat6", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 28, name = "lambov10", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 29, name = "rotary7", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 30, name = "supra2jzgtett", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 31, name = "m158huayra", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 32, name = "viperv10", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 33, name = "veyronsound", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 34, name = "perfov10", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 35, name = "sestov10", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 36, name = "mclarenv8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 37, name = "murciev12", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 38, name = "r35sound", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 39, name = "musv8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 40, name = "apollosv8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 41, name = "avesvv12", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 42, name = "diablov12", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 43, name = "f40v8", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 44, name = "f50v12", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 45, name = "ferrarif12", basePrice = 1500, count = 0, maxCount = 100 },
    { slot = 46, name = "gtaspanov10", basePrice = 1500, count = 0, maxCount = 100 }
}

-- Helper function to get item base price
function Config.GetBasePrice(itemName)
    for _, item in ipairs(Config.Items) do
        if item.name == itemName then
            return item.basePrice
        end
    end
    return 0
end

-- Helper function to get item buy price
function Config.GetBuyPrice(itemName)
    return Config.GetBasePrice(itemName) * Config.ShopMultiplier
end

-- Shop Location
Config.ShopLocation = {
    coords = vector4(-149.998, -1345.459, 29.883, 172.55),
    ped = 'mp_m_waremech_01',
    target = {
        name = 'tuning_shop', -- Target zone name
        label = 'Tuning Shop', -- Interaction label
        icon = 'fa-solid fa-wrench' -- Interaction icon
    },
    blip = {
        name = "Tuning Shop",
        sprite = 446, -- Wrench sprite
        color = 5, -- Yellow color
        scale = 0.7
    }
}

-- UI Labels
Config.UI = {
    mainMenu = {
        buyTitle = "Buy Items",
        buyDescription = "Browse items to purchase",
        sellTitle = "Sell Items",
        sellDescription = "Sell items to the shop"
    },
    sellMenu = {
        title = 'Tuning Shop - Sell Items',
        sellFormat = 'Sell all for $%d each',
        successMessage = 'Items sold successfully!',
        errorMessage = 'The shop is not buying this right now!'
    }
} 