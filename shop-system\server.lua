local Framework = exports['no-core']:GetCoreObject()
Framework.Shared.Items = exports.ox_inventory:Items()

-- Check if shop exists in database
local function EnsureShopExists()
    local result = MySQL.scalar.await('SELECT 1 FROM ox_shops WHERE name = ?', {Config.Shop.id})
    if not result then
        -- Shop doesn't exist, create it with default items
        local defaultItems = {}
        for _, item in ipairs(Config.Items) do
            defaultItems[#defaultItems + 1] = {
                name = item.name,
                slot = item.slot,
                count = item.count,
                price = Config.GetBuyPrice(item.name)
            }
        end
        
        MySQL.insert.await('INSERT INTO ox_shops (name, items) VALUES (?, ?)', {
            Config.Shop.id,
            json.encode(defaultItems)
        })
        
        if Config.Debug then
            print('^2Shop created in database: ^7' .. Config.Shop.id)
        end
    end
end

-- Initialize shop stash
local function InitializeShop()
    Wait(2000)
    
    -- Ensure shop exists in database
    EnsureShopExists()
    
    local stash = {
        id = Config.Shop.stash.id,
        label = Config.Shop.stash.label,
        slots = #Config.Items,
        weight = Config.Shop.stash.weight,
    }

    exports.ox_inventory:RegisterStash(stash.id, stash.label, stash.slots, stash.weight, true)
    
    -- Load shop items from database
    local ShopItems = {}
    local result = MySQL.single.await('SELECT items FROM ox_shops WHERE name = ?', {Config.Shop.id})
    if result then
        local Items = json.decode(result.items) or Config.Items
        for _, item in pairs(Items) do
            ShopItems[#ShopItems + 1] = {
                slot = item.slot,
                name = item.name,
                price = Config.GetBuyPrice(item.name),
                count = item.count
            }
        end
    end

    -- Register the shop
    exports.ox_inventory:RegisterShop(Config.Shop.type, {
        name = Config.Shop.label,
        inventory = ShopItems,
        coords = Config.ShopLocation.coords.xyz
    })
end

-- Save shop state to database
local function SaveShop()
    local Items = exports.ox_inventory:GetShopItems(Config.Shop.type)
    MySQL.update.await('UPDATE ox_shops SET items = ? WHERE name = ?', {json.encode(Items), Config.Shop.id})
end

-- Event handlers for saving shop state
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        InitializeShop()
    end
end)

AddEventHandler('playerDropped', function()
    if GetNumPlayerIndices() == 0 then
        SaveShop()
    end
end)

AddEventHandler('txAdmin:events:serverShuttingDown', function()
    SaveShop()
end)

AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        SaveShop()
    end
end)

-- Selling items callback
lib.callback.register("shop-system:Selling", function(source, data)
    local src = source
    local Player = Framework.Functions.GetPlayer(src)
    local Items = exports.ox_inventory:GetShopItems(Config.Shop.type)
    local CurrentCount = 0
    local MaxCount = 0

    -- Find current and max count for the item
    if Items then
        for i, item in ipairs(Items) do
            if item.name == data.item then
                CurrentCount = item.count
                for _, configItem in ipairs(Config.Items) do
                    if configItem.name == data.item then
                        MaxCount = configItem.maxCount
                        break
                    end
                end
                break
            end
        end
    end

    if not Player then return false end

    -- Check if player has the item and shop can accept more
    local item = exports.ox_inventory:GetItem(src, data.item, nil, false)
    if item and item.count >= data.count and CurrentCount + data.count <= MaxCount then
        local pay = (data.count * Config.GetBasePrice(data.item))
        
        -- Remove item from player and add to shop
        if exports.ox_inventory:RemoveItem(src, data.item, data.count) then 
            exports.ox_inventory:AddShopCount(Config.Shop.type, data.item, data.count)
            Player.Functions.AddMoney('cash', pay)
            return true
        end
    else
        TriggerClientEvent('Framework:Notify', src, Config.UI.sellMenu.errorMessage, "error")
    end
    return false
end) 