local itemPool = {
    [1] = "nitrous_install_kit",
    [2] = "nitrous_bottle",
    [3] = "fakeplate",
    [4] = "lighting_controller",
    [5] = "duct_tape",
    [6] = "engine_oil",
    [7] = "tyre_replacement",
    [8] = "clutch_replacement",
    [9] = "air_filter",
    [10] = "spark_plug",
    [11] = "brakepad_replacement",
    [12] = "suspension_parts",
    [13] = "awd_drivetrain",
    [14] = "rwd_drivetrain",
    [15] = "fwd_drivetrain",
    [16] = "slick_tyres",
    [17] = "semi_slick_tyres",
    [18] = "offroad_tyres",
    [19] = "drift_tuning_kit",
    [20] = "r488sound",
    [21] = "k20a",
    [22] = "urusv8",
    [23] = "m297zonda",
    [24] = "v8engine",
    [25] = "shonen",
    [26] = "predatorv8",
    [27] = "gt3flat6",
    [28] = "lambov10",
    [29] = "rotary7",
    [30] = "supra2jzgtett",
    [31] = "m158huayra",
    [32] = "viperv10",
    [33] = "veyronsound",
    [34] = "perfov10",
    [35] = "sestov10",
    [36] = "mclarenv8",
    [37] = "murciev12",
    [38] = "r35sound",
    [39] = "musv8",
    [40] = "apollosv8",
    [41] = "avesvv12",
    [42] = "diablov12",
    [43] = "f40v8",
    [44] = "f50v12",
    [45] = "ferrarif12",
    [46] = "gtaspanov10",
    [47] = "crypto-converter",
}

function ChanceForItem(playerId, Player)
    local chance = math.random(1, 15)
    if chance >= 9 then
        local Item = math.random(1, 100)
        if Item <= 10 then
            local metadata = { description = string.format('Uses: %s', "100"), uses = 100}
            exports.ox_inventory:AddItem(playerId, itemPool[1], 1, metadata)
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "red", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemPool[1])
            return
        elseif Item <= 100 then
            local itemToGive = itemPool[math.random(2, #itemPool-1)]
            if itemToGive == itemPool[3] then
                exports.ox_inventory:AddItem(playerId, itemToGive, 1)
            else
                exports.ox_inventory:AddItem(playerId, itemToGive, 1)
            end
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "red", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemToGive)
            return
        end
    end
end

function ChanceForCrypto(playerId, Player)
    local chance = math.random(1, 15)
    if chance >= 8 then
       local Item = math.random(1, 100)
       if Item <= 65 then
            exports.ox_inventory:AddItem(playerId, itemPool[47], 1)
            Wait(100)
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "orange", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemPool[47])
            return
       end
    end
end

function addincome(cid, crypto)
    local data = MySQL.query.await('SELECT EXISTS(SELECT 1 FROM player_dailyincome WHERE cid = ? ) as exist', {cid})
    if data[1].exist == 1 then
        MySQL.query('UPDATE player_dailyincome SET crypto = crypto + ? WHERE cid = ?', {crypto, cid})
    else
        MySQL.query('INSERT INTO player_dailyincome (cid, crypto) VALUES (?, ?)', {cid, crypto})
    end
end

function checkincome(cid, src)
    local vipRole = 1372897098386640936
    local hasVipRole = exports["no-core"]:UserHasRole(src, vipRole)

    local cryptoLimit = 800
    if hasVipRole then
        cryptoLimit = cryptoLimit * 1.75
        if UseDebug then print('^2Player', src, 'is VIP via Discord role check, increasing crypto limit^0') end
    end

    local data = MySQL.query.await('SELECT EXISTS(SELECT 1 FROM player_dailyincome WHERE cid = ? ) as exist', {cid})
    if data[1].exist == 1 then
        local data2 = MySQL.query.await("SELECT * FROM player_dailyincome WHERE cid = '" .. cid .. "'")
        if data2[1].crypto > cryptoLimit then
            return false
        else
            return true
        end
    else
        return true
    end
end

-- Boosting Points Reward System (Updated to match racing-server)
function giveCurated(src, racers, position, raceMoney, racerName)
    print('^2giveCurated^0', src, racers, position, raceMoney, racerName)
    if racers < 0 then return end

    local playerCitizenId = getCitizenId(src)
    if not playerCitizenId then
        if UseDebug then print('^1Failed to get citizen ID for player', src, '^0') end
        return
    end

    local placePoints = {
        [1] = 3,
        [2] = 2.5,
        [3] = 2,
    }
    local multiplier = placePoints[position] or 1.66
    if not checkincome(playerCitizenId, src) then
        multiplier = 1
    end
    local pointsAwarded = math.max(1, math.floor(((raceMoney or 0)/100)*multiplier + 0.5))

    -- Check if user exists, create if not
    local success, result = pcall(function()
        return MySQL.scalar.await('SELECT crypto FROM ra_boosting_user_settings WHERE player_identifier = ?', {playerCitizenId})
    end)

    if success then
        if result then
            -- User exists, update crypto
            MySQL.query.await('UPDATE ra_boosting_user_settings SET crypto = crypto + ? WHERE player_identifier = ?', {pointsAwarded, playerCitizenId})
        else
            TriggerClientEvent('Framework:Notify', src, 'User not found', 'error', 5000)
            return
            -- -- User doesn't exist, create record
            -- MySQL.query.await('INSERT INTO ra_boosting_user_settings (player_identifier, crypto, daily_income) VALUES (?, ?, 0)', {playerCitizenId, pointsAwarded})
        end

        addincome(playerCitizenId, pointsAwarded)
        local Player = GetPlayer(src)
        if Player then
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "green",
                string.format("**Player** %s (citizenid: *%s*)\n**Retrieved: %d Boosting coins\n**Position: %d/%d**",
                    GetPlayerName(src), Player.PlayerData.citizenid, pointsAwarded, position, racers))
            TriggerClientEvent('cw-racingapp:client:notify', src,
                string.format('You received %d boosting coins for finishing %s place!', pointsAwarded,
                    position == 1 and "1st" or position == 2 and "2nd" or position == 3 and "3rd" or position.."th"), 'success')

            if position >= 1 and position <= 3 then
               ChanceForItem(src, Player)
            end
            Wait(100)
            ChanceForCrypto(src, Player)
        end
    else
        if UseDebug then print('^1Database error in giveCurated for player', src, '^0') end
        TriggerClientEvent('cw-racingapp:client:notify', src, 'Database error occurred', 'error')
    end
    Wait(250)
end
