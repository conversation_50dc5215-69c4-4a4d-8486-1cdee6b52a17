body {
  user-select: none;
}

.allow-select {
  user-select: text;
}
.inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.standard-margin-bot {
  margin-bottom: 10px;
}

.underlined {
  border-bottom: $border;
}

.bold {
  font-weight: bold;
}

.full-width {
  width: 100%;
}

.center-text {
  text-align: center;
}

.centered {
  justify-content: center;
}

.spaced {
  justify-content: space-between;
}

.clickableText {
  cursor: pointer;
}

.clickableText:hover {
  cursor: pointer;
  color: $text-color-disabled;
}

.standardGap {
  gap: 5px;
}

.tablinks {
  background-color: $background-color;
}

.tablink {
  background: none;
  font-weight: bold;
  float: left;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 14px 16px;
  font-size: 17px;
  width: 100%;
  color: $text-color;
}

.tablink:hover {
  background-color: $background-color-lighter;
}

.subheader {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card {
  color: $text-color;
  width: 258px;
  height: 100px;
  padding: 15px;
  border-radius: $border-radius;
  border: $border;
}

.card-header {
  margin-bottom: 10px;
  font-weight: bold;
}

.tabcontent {
  color: $text-color;
  width: 100%;
  padding: 1em;
}

/* LOADING RING */

.loading-container {
  display: none;
  justify-content: center;
}
.loader {
  width: 48px;
  height: 48px;
  border: 3px solid #fff;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
.loader::after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-bottom-color: $primary-color;
}

.header-text {
  flex-grow: 1;
}

.scrollable {
  overflow-y: auto;
}

.pagecontent {
  height: calc(80vh - 42px - 2em);
}

.page-container {
  margin-left: auto;
  margin-right: auto;
  width: 95%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: auto;
  max-height: calc(80vh - 120px - 2em);
}

// .v-card{
//   background-color: rgba(255, 255, 255, .15) !important;  
//   backdrop-filter: blur(5px) !important;
// } 

.v-table {
  border-radius: 1em;
}

.v-overlay__scrim {
  background: rgb(0 0 0 / 52%);
  backdrop-filter: saturate(180%) blur(10px);
  opacity: 100;
}