{"compilerOptions": {"baseUrl": ".", "target": "esnext", "useDefineForClassFields": true, "allowSyntheticDefaultImports": true, "composite": true, "skipLibCheck": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": false, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["node", "vuetify", "vite/client"], "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "vite.config.ts"]}