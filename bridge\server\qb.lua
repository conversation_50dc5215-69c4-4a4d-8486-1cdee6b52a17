if GetResourceState('no-core') ~= 'started' or GetResourceState('qbx_core') == 'started' then return end

QBCore = exports['no-core']:GetCoreObject()
QBCore.Functions.CreateUseableItem(Config.ItemName.gps, function(source, item)
    openRacingApp(source)
end)


-- Adds money to user
function addMoney(src, moneyType, amount)
    local Player = QBCore.Functions.GetPlayer(src)
    if moneyType == 'black_money' then
        -- Use ox_inventory for black money
        exports.ox_inventory:AddItem(src, 'black_money', math.floor(amount))
    else
        Player.Functions.AddMoney(moneyType, math.floor(amount))
    end
end

-- Removes money from user
function removeMoney(src, moneyType, amount, reason)
    local Player = QBCore.Functions.GetPlayer(src)
    if moneyType == 'black_money' then
        -- Use ox_inventory for black money
        return exports.ox_inventory:RemoveItem(src, 'black_money', math.floor(amount))
    else
        return Player.Functions.RemoveMoney(moneyType, math.floor(amount))
    end
end

-- Checks that user can pay
function canPay(src, moneyType, cost)
    local Player = QBCore.Functions.GetPlayer(src)
    if moneyType == 'black_money' then
        -- Check ox_inventory for black money
        local item = exports.ox_inventory:GetItem(src, 'black_money', nil, false)
        return item and item.count >= cost
    else
        return Player.PlayerData.money[moneyType] >= cost
    end
end

-- Fetches the CitizenId by Source
function getCitizenId(src)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return nil end
    return Player.PlayerData.citizenid
end

-- Fetches the Source of an online player by citizenid
function getSrcOfPlayerByCitizenId(citizenId)
    local Player = QBCore.Functions.GetPlayerByCitizenId(citizenId)
    if not Player then return nil end
    return Player.PlayerData.source
end

function GetPlayer(src)
    return QBCore.Functions.GetPlayer(src)
end
