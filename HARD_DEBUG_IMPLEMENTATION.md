# Hard Debug Implementation for Racing App

## Overview
I've implemented a comprehensive hard debug system for the racing application that provides detailed information about race starts and finishes. This system is separate from the regular debug system and can be controlled independently.

## Configuration
- **Location**: `shared/config.lua`
- **Setting**: `Config.HardDebug = true/false`
- **Default**: Currently set to `true` for testing (change to `false` for production)

## Features Implemented

### 1. Race Start Debug Messages
**Location**: `server/main.lua` - `RegisterNetEvent('cw-racingapp:server:startRace')`

**Information Displayed**:
- Race ID
- Track ID and Name
- Curated status (YES/NO)
- Buy-in amount
- Total number of racers
- **For each participant**:
  - Player name
  - Citizen ID
  - Source ID
  - Money deposited
  - Racing crew (if applicable)

**Example Output**:
```
========================================
           RACE START DEBUG           
========================================
Race ID: RI-12345
Track ID: LR-7666
Track Name: Elysian Island Circuit
Curated: YES
Buy-in Amount: $5000
Total Racers: 4
--- PARTICIPANTS ---
Player 1:
  Name: SpeedRacer123
  Citizen ID: ABC12345
  Source: 1
  Money Deposited: $5000
  Crew: StreetKings
Player 2:
  Name: FastDriver99
  Citizen ID: DEF67890
  Source: 2
  Money Deposited: $5000
========================================
```

### 2. Individual Player Finish Debug Messages
**Location**: `server/main.lua` - `RegisterNetEvent('cw-racingapp:server:finishPlayer')`

**Information Displayed**:
- Race ID
- Racer name and Citizen ID
- Total race time
- Best lap time
- Vehicle model
- Car class
- Racing crew
- Curated race status

**Example Output**:
```
========================================
         PLAYER FINISH DEBUG          
========================================
Race ID: RI-12345
Racer Name: SpeedRacer123
Citizen ID: ABC12345
Total Time: 125430ms
Best Lap: 41250ms
Vehicle: Osiris FR
Car Class: X
Racing Crew: StreetKings
Curated Race: YES
========================================
```

### 3. Complete Race Finish Debug Messages
**Location**: `server/main.lua` - When all players have finished

**Information Displayed**:
- Race summary (ID, track, participants, prize pool)
- Curated and ranked status
- **Final results for each player**:
  - Final position
  - Race time and best lap
  - Vehicle used
  - Money rewards (based on prize pool distribution)
  - Curated points awarded (for curated races)

**Example Output**:
```
========================================
         RACE COMPLETE DEBUG          
========================================
Race ID: RI-12345
Track: Elysian Island Circuit
Total Participants: 4
Initial Racer Count: 4
Buy-in Amount: $5000
Total Prize Pool: $20000
Curated Race: YES
Ranked Race: YES
--- FINAL RESULTS ---
Place 1:
  Name: SpeedRacer123
  Time: 125430ms
  Best Lap: 41250ms
  Vehicle: Osiris FR
  Money Reward: $12000
  Curated Points: 150
Place 2:
  Name: FastDriver99
  Time: 127890ms
  Best Lap: 42100ms
  Vehicle: Zentorno
  Money Reward: $6000
  Curated Points: 125
Place 3:
  Name: RaceKing88
  Time: 130250ms
  Best Lap: 43500ms
  Vehicle: T20
  Money Reward: $2000
  Curated Points: 100
========================================
```

### 4. Head-to-Head (H2H) Race Debug Messages
**Location**: `server/head2head.lua`

**H2H Race Start**:
- Race ID and type
- Buy-in amount and total prize pool
- Participant details

**H2H Race Finish**:
- Individual finish information
- Winner announcement with prize money
- Race completion summary

## Technical Implementation

### Variables Added
- `shared/config.lua`: `Config.HardDebug`
- `server/main.lua`: `local UseHardDebug = Config.HardDebug`
- `server/head2head.lua`: `local useHardDebug = Config.HardDebug`

### Color Coding
- `^1` - Red (headers and separators)
- `^2` - Green (section headers)
- `^3` - Yellow (field labels)
- `^5` - Magenta (values)
- `^6` - Cyan (player/position indicators)
- `^0` - Reset to default color

## Usage
1. Set `Config.HardDebug = true` in `shared/config.lua`
2. Restart the resource
3. Start any race to see comprehensive debug output
4. Check server console for detailed race information

## Benefits
- **Debugging**: Easy identification of race issues
- **Monitoring**: Track player participation and money flow
- **Analytics**: Detailed race completion data
- **Troubleshooting**: Comprehensive information for support

## Production Notes
- Set `Config.HardDebug = false` in production to disable verbose output
- The system is independent of the regular debug system
- No performance impact when disabled
- All debug messages are server-side only
